# SPDX-FileCopyrightText: Copyright (c) 2023 - 2024 NVIDIA CORPORATION & AFFILIATES.
# SPDX-FileCopyrightText: All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

hydra:
  job:
    chdir: true
    name: graphcast
  run:
    dir: ./outputs/${hydra:job.name}


# ┌───────────────────────────────────────────┐
# │            Model Configuration            │
# └───────────────────────────────────────────┘

processor_layers: 16              # Number of processor layers.
hidden_dim: 512                   # Size of each layer.
mesh_level: 6                     # Max icosphere level in the multimesh.
multimesh: true                   # If true, uses multimesh for the processor.
processor_type: MessagePassing    # "GraphTransformer" as in GenCast, or "MessagePassing" as in GraphCast.
khop_neighbors: 32                # Number of neighbors for each node used in the GraphTransformer. Only used if the processor type is "GraphTransformer".  
num_attention_heads: 4            # Number of attention heads. Only used if the processor type is "GraphTransformer".
norm_type: TELayerNorm            # "TELayerNorm" or "LayerNorm". Use "TELayerNorm" for improved performance.


# ┌───────────────────────────────────────────┐
# │   Gradient Checkpointing Configuration    │
# └───────────────────────────────────────────┘

segments: 1                      # Number of segments in gradient checkpointing for the processor. Only used if "checkpoint_processor" is true.
force_single_checkpoint: false   # If true, applies single-segment end-to-end gradient checkpointing.
checkpoint_encoder: true         # If true, applies single-segment gradient checkpointing for the embedder, encoder, and the first layer of the processor combined.
checkpoint_processor: false      # If true, applies gradient checkpointing for the processor, excluding first and last layers. "segments" controls the number of segments for gradient checkpointing.
checkpoint_decoder: false        # If true, applies single-segment gradient checkpointing for the last layer of the processor, decoder, and the final layer combined.
force_single_checkpoint_finetune: false   # If true, applies single-segment end-to-end gradient checkpointing for fine-tuning (multi-step rollout).
checkpoint_encoder_finetune: true         # If true, applies single-segment gradient checkpointing for fine-tuning for the embedder, encoder, and the first layer of the processor combined.
checkpoint_processor_finetune: true       # If true, applies gradient checkpointing for fine-tuning for the processor, excluding first and last layers. "segments" controls the number of segments for gradient checkpointing.
checkpoint_decoder_finetune: true         # If true, applies single-segment gradient checkpointing for fine-tuning for the last layer of the processor, decoder, and the final layer combined.


# ┌───────────────────────────────────────────┐
# │      Performance and Optimization         │
# └───────────────────────────────────────────┘

concat_trick: true          # If true, uses a concatenation trick to reduce memory overhead and improve MLP layer performance on the source, destination node features, and edge features.
                            # See https://docs.dgl.ai/guide/message-efficient.html for more info.
cugraphops_encoder: false   # If true, uses cugraphops backend for the encoder.
cugraphops_processor: false # If true, uses cugraphops backend for the processor.
cugraphops_decoder: false   # If true, uses cugraphops backend for the decoder.
recompute_activation: true  # If true, recomputes activation in backward to save memory. Currently, only SiLU is supported.
use_apex: true              # If true, uses Apex for fused Adam optimizer, typically resulting in 10-20% faster training iterations.
                            # Apex is pre-installed in PhysicsNeMo containers.

# ┌───────────────────────────────────────────┐
# │           Dataset Configuration           │
# └───────────────────────────────────────────┘

dataset_path: /data/era5_75var             # Path to the dataset.
static_dataset_path: static                # Path to the static datasets. Includes .nc files for land-sea mask and geopotential.
dataset_metadata_path: /data/era5_75var/metadata/data.json  # Path to the dataset metadata, containing channel names.
time_diff_std_path: /time_diff_std.npy  # Path to the .npy file with standard deviation of normalized per-variable per-pressure level time differences.


# ┌───────────────────────────────────────────┐
# │      Data and Sampling Configuration      │
# └───────────────────────────────────────────┘

latlon_res: [721, 1440]     # Resolution of the latitude-longitude grid. If smaller than the native resolution, bilinear interpolation is applied.
num_samples_per_year_train: 1408  # Number of samples per year for training.
num_workers: 8              # Number of subprocesses to use for data loading. 0 means data is loaded in the main process.
num_channels_climate: 73    # Number of climate channels.
num_channels_static: 5      # Number of static channels (e.g., land-sea mask, geopotential, cosine of latitudes, sine and cosine of longitudes).
num_channels_val: 3         # Number of channels used for light-weight validation during training.
num_val_steps: 8            # Number of rollouts used in light-weight validation during training.
num_val_spy: 3              # Number of samples per year used as the initial condition in light-weight validation during training.
num_history: 0              # Number of historical (previous time steps) to use. With history=1, the model uses t-1 and t to predict t+1.
use_cos_zenith: true        # If true, uses cosine zenith angle as additional channel(s). It can replace the total incident solar radiation.
dt: 6.0                     # Time in hours between each timestep in the dataset. A dt of 6.0 means four timesteps per day.
start_year: 1980            # Start year of the dataset, used in computing the cosine zenith angle.
stride: 1                   # Number of steps between input and output variables. For example, if data is every 6 hours, stride 1 = 6 hour delta t, and stride 2 = 12 hours delta t.
use_time_of_year_index: true  # If true, the dataloader also gives the index of the sample for calculating the time of day and year progress.


# ┌───────────────────────────────────────────┐
# │           Training Configuration          │
# └───────────────────────────────────────────┘

grad_clip_norm: 32.0        # Threshold for gradient clipping.
jit: false                  # If true, uses JIT compilation.
amp: false                  # If true, uses AMP.
amp_dtype: bfloat16         # Data type to use with AMP if "amp" is true.
full_bf16: true             # If true, uses bfloat16 for the entire training.
lr: 1e-3                    # Max learning rate in the learning rate schedule. Starts from zero to "lr" within "num_iters_step1" steps, then decays with a cosine schedule in "num_iters_step2" steps, reaching "lr_step3".
lr_step3: 3e-7              # Min learning rate in the learning rate schedule.
num_iters_step1: 1000       # Number of iterations (backward passes) in the first phase of the learning rate schedule.
num_iters_step2: 299000     # Number of iterations (backward passes) in the second phase of the learning rate schedule.
num_iters_step3: 11000      # Number of iterations (backward passes) for incremental fine-tuning, with increments of "step_change_freq".
step_change_freq: 1000      # Frequency of increments for multi-step fine-tuning.
save_freq: 1              # Frequency (iterations) for saving network checkpoints.
val_freq: 5               # Frequency (iterations) for performing light-weight validation during training.


# ┌───────────────────────────────────────────┐
# │          Logging and Monitoring           │
# └───────────────────────────────────────────┘

wb_mode: online            # Weights and Biases mode ["online", "offline", "disabled"]. If you don’t have a Weights and Biases API key, set this to "disabled".
watch_model: false         # If true, records the model parameter gradients through Weights and Biases.


# ┌───────────────────────────────────────────┐
# │         Checkpoint Configuration          │
# └───────────────────────────────────────────┘

ckpt_path: checkpoints      # Directory for saving model checkpoints.
val_dir: validation         # Directory for saving light-weight validation plots.
ckpt_name: model            # Name of the checkpoints.


# ┌───────────────────────────────────────────┐
# │          Debugging and Profiling          │
# └───────────────────────────────────────────┘

synthetic_dataset: false    # If true, uses a synthetic dataset for debugging and performance benchmarking.
pyt_profiler: false         # If true, uses PyTorch profiler.
profile: false              # If true, uses NVIDIA Nsight systems for profiling.
profile_range: (90, 110)    # Range of iterations for profiling.
