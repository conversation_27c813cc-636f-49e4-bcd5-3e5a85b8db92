# SPDX-FileCopyrightText: Copyright (c) 2023 - 2024 NVIDIA CORPORATION & AFFILIATES.
# SPDX-FileCopyrightText: All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ruff: noqa

import warnings

warnings.warn(
    "physicsnemo.utils.generative is deprecated and will be removed in a future version. "
    "Please use physicsnemo.utils.diffusion instead."
)

from physicsnemo.utils.diffusion.deterministic_sampler import deterministic_sampler
from physicsnemo.utils.diffusion.stochastic_sampler import stochastic_sampler
from physicsnemo.utils.diffusion.utils import (
    EasyDict,
    InfiniteSampler,
    StackedRandomGenerator,
    assert_shape,
    call_func_by_name,
    check_ddp_consistency,
    constant,
    construct_class_by_name,
    convert_datetime_to_cftime,
    copy_files_and_create_dirs,
    copy_params_and_buffers,
    ddp_sync,
    format_time,
    format_time_brief,
    get_dtype_and_ctype,
    get_module_dir_by_obj_name,
    get_module_from_obj_name,
    get_obj_by_name,
    get_obj_from_module,
    get_top_level_function_name,
    is_top_level_function,
    list_dir_recursively_with_ignore,
    named_params_and_buffers,
    params_and_buffers,
    parse_int_list,
    print_module_summary,
    profiled_function,
    suppress_tracer_warnings,
    time_range,
    tuple_product,
)
