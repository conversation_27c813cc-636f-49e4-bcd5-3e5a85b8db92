# GraphCast 增强集成指南

## 快速开始实现

本指南提供了将增强 GraphCast 配置集成到现有 PhysicsNemo 代码库的分步说明。

## 1. 所需文件修改

### 步骤 1：添加增强损失函数

将 `enhanced_graphcast_loss.py` 文件复制到：
```
graphcast_physicsnemo/physicsnemo/utils/graphcast/enhanced_loss.py
```

### 步骤 2：更新训练配置

创建增强配置文件 `conf/config_enhanced.yaml`：

```yaml
# 从 GraphCast_Training_Configuration_Guide.md 复制
# 对现有 config.yaml 的关键添加：

# 增强损失配置
loss_type: "enhanced_graphcast"
enable_residual_connections: true
enable_latitude_weighting: true  
enable_pressure_weighting: true
enable_variable_weighting: true
loss_log_frequency: 1000

# 残差连接的输入变量
input_variables:
  - "2m_temperature"
  - "10m_u_component_of_wind" 
  - "10m_v_component_of_wind"
  - "mean_sea_level_pressure"
  - "geopotential"
  - "specific_humidity"
  - "temperature"
  - "u_component_of_wind"
  - "v_component_of_wind"
  - "vertical_velocity"

# 官方压力层（13 层）
pressure_levels: [50, 100, 150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000]
num_pressure_levels: 13

# 架构修复
norm_type: LayerNorm  # 从 TELayerNorm 更改以确保准确性
processor_layers: 16
hidden_dim: 512
```

### 步骤 3：修改训练脚本

更新 `train_graphcast.py`：

```python
# 在顶部添加导入
from physicsnemo.utils.graphcast.enhanced_loss import EnhancedGraphCastLoss

# 在 GraphCastTrainer.__init__() 中，替换损失初始化：
def __init__(self, cfg, dist, rank_zero_logger):
    # ... 现有初始化代码 ...
    
    # 增强损失函数初始化
    if getattr(cfg, 'loss_type', 'original') == 'enhanced_graphcast':
        # 获取用于权重的纬度坐标
        lat_coords = torch.linspace(-90, 90, cfg.latlon_res[0])
        
        self.criterion = EnhancedGraphCastLoss(
            config=cfg,
            area=self.area,
            channels_list=self.channels_list,
            dataset_metadata_path=cfg.dataset_metadata_path,
            time_diff_std_path=cfg.time_diff_std_path,
            input_variables=cfg.input_variables,
            lat_coords=lat_coords
        )
        rank_zero_logger.info("使用增强 GraphCast 损失函数")
    else:
        # 原始损失函数
        if cfg.synthetic_dataset:
            self.criterion = CellAreaWeightedLossFunction(self.area)
        else:
            self.criterion = GraphCastLossFunction(
                self.area,
                self.channels_list,
                cfg.dataset_metadata_path,
                cfg.time_diff_std_path,
            )
        rank_zero_logger.info("使用原始损失函数")

# 修改训练步骤以传递残差连接的输入
def train(self, invar, outvar):
    """支持残差连接的修改训练步骤"""
    self.model.train()
    self.optimizer.zero_grad()
    
    # 前向传播
    pred = self.model(invar)
    
    # 使用残差连接输入的增强损失计算
    if hasattr(self.criterion, 'config') and getattr(self.criterion.config, 'enable_residual_connections', False):
        loss = self.criterion(pred, outvar, inputs=invar)
    else:
        loss = self.criterion(pred, outvar)
    
    # 反向传播
    if self.scaler:
        self.scaler.scale(loss).backward()
        self.scaler.unscale_(self.optimizer)
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip_norm)
        self.scaler.step(self.optimizer)
        self.scaler.update()
    else:
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.grad_clip_norm)
        self.optimizer.step()
    
    self.scheduler.step()
    return loss
```

### 步骤 4：更新模型配置

修改 `physicsnemo/models/graphcast/graph_cast_net.py`：

```python
# 在 GraphCastNet.__init__() 中，添加参数验证：
def __init__(self, **kwargs):
    # 确保官方架构参数以保证准确性
    if kwargs.get('enforce_official_params', True):
        official_params = {
            'processor_layers': 16,
            'hidden_dim': 512, 
            'hidden_layers': 1,
            'activation_fn': 'silu',
            'norm_type': 'LayerNorm',  # 关键：不是 TELayerNorm
            'aggregation': 'sum',
        }
        
        for param, value in official_params.items():
            if param in kwargs and kwargs[param] != value:
                print(f"警告：为了官方准确性，将 {param} 从 {kwargs[param]} 覆盖为 {value}")
            kwargs[param] = value
    
    super().__init__(**kwargs)
```

## 2. 渐进式训练实现

### 阶段 1：6层概念验证

创建 `conf/config_6level.yaml`：

```yaml
# 继承增强配置
defaults:
  - config_enhanced

# 6层训练覆盖设置
pressure_levels: [200, 300, 500, 700, 850, 1000]
num_pressure_levels: 6
num_channels_climate: 41  # 6*6 + 5 = 41 通道

# 调整的训练参数
batch_size: 2
lr: 2e-3
num_iters_step2: 500000

# 内存优化
gradient_checkpointing: true
accumulate_grad_batches: 2
```

### 阶段 2：渐进式扩展

创建 `conf/config_11level.yaml`：

```yaml
# 继承增强配置
defaults:
  - config_enhanced

# 11层配置
pressure_levels: [150, 200, 250, 300, 400, 500, 600, 700, 850, 925, 1000]
num_pressure_levels: 11
num_channels_climate: 71  # 6*11 + 5 = 71 通道

# 微调参数
batch_size: 1
lr: 5e-4
load_checkpoint: true
checkpoint_path: "checkpoints/6level_final.pt"
```

### 阶段 3：完整分辨率

使用主要的 `config_enhanced.yaml` 配置：

```yaml
# 完整13层配置
load_checkpoint: true
checkpoint_path: "checkpoints/11level_final.pt"
lr: 1e-4  # 微调学习率
```

## 3. 训练命令

### 单GPU训练

```bash
# 阶段1：6层训练
python train_graphcast.py --config-name=config_6level

# 阶段2：11层训练  
python train_graphcast.py --config-name=config_11level

# 阶段3：完整13层训练
python train_graphcast.py --config-name=config_enhanced
```

### 多GPU训练

```bash
# 阶段1：6层训练（4个GPU）
torchrun --standalone --nnodes=1 --nproc_per_node=4 \
    train_graphcast.py --config-name=config_6level

# 阶段2：11层训练（由于内存限制使用2个GPU）
torchrun --standalone --nnodes=1 --nproc_per_node=2 \
    train_graphcast.py --config-name=config_11level

# 阶段3：完整训练（由于内存限制使用1个GPU）
python train_graphcast.py --config-name=config_enhanced
```

## 4. 验证和测试

### 增强验证脚本

创建 `validate_enhanced.py`：

```python
import torch
import numpy as np
from physicsnemo.utils.graphcast.enhanced_loss import EnhancedGraphCastLoss

def validate_loss_implementation():
    """验证增强损失产生预期行为"""
    
    # 创建虚拟数据
    batch_size, channels, height, width = 1, 83, 721, 1440
    prediction = torch.randn(batch_size, channels, height, width)
    target = torch.randn(batch_size, channels, height, width)
    inputs = torch.randn(batch_size, 2, channels, height, width)  # 2个时间步
    
    # 测试配置
    class Config:
        enable_residual_connections = True
        enable_latitude_weighting = True
        enable_pressure_weighting = True
        enable_variable_weighting = True
    
    config = Config()
    
    # 创建损失函数（为测试简化）
    area = torch.ones(height, width)
    channels_list = list(range(channels))
    
    # 实际使用中需要真实的元数据文件
    print("增强损失验证需要实际的元数据文件")
    print("关键验证点：")
    print("1. 残差连接减少损失幅度")
    print("2. 纬度权重强调赤道区域")  
    print("3. 压力权重强调表面层")
    print("4. 变量权重匹配官方规范")

if __name__ == "__main__":
    validate_loss_implementation()
```

### 性能监控

添加到训练脚本中：

```python
# 训练循环中的增强日志记录
if iter % cfg.val_freq == 0:
    # 详细损失分解
    with torch.no_grad():
        detailed_loss, loss_dict = trainer.criterion(
            pred, target, inputs=invar, return_detailed_losses=True
        )
        
        # 记录每个变量的损失
        for var_name, var_loss in loss_dict.items():
            wandb.log({var_name: var_loss.item()}, step=iter)
        
        # 记录压力层分解
        if hasattr(trainer.criterion, 'compute_pressure_level_losses'):
            level_losses = trainer.criterion.compute_pressure_level_losses(pred, target)
            for level, loss_val in level_losses.items():
                wandb.log({f"loss_level_{level}": loss_val.item()}, step=iter)
```

## 5. 预期结果和监控

### 关键跟踪指标

1. **整体RMSE**：通过所有修复应改善15-25%
2. **每变量RMSE**：分别跟踪温度、风速、压力  
3. **每层RMSE**：监控大气层性能
4. **训练稳定性**：梯度范数、损失收敛
5. **内存使用**：监控GPU内存消耗

### 成功指标

- **损失收敛**：平滑下降无振荡
- **梯度健康**：梯度范数在[0.1, 10.0]范围内
- **变量平衡**：无单一变量主导损失
- **层级平衡**：表面和高层损失平衡
- **验证改善**：一致的验证RMSE减少

### 常见问题故障排除

1. **内存问题**：减少batch_size，启用gradient_checkpointing
2. **训练不稳定**：降低学习率，检查梯度裁剪
3. **收敛差**：验证残差连接，检查数据归一化
4. **变量不平衡**：调整变量权重，检查压力权重

## 6. 时间线和里程碑

### 第1-2周：实现
- [ ] 集成增强损失函数
- [ ] 更新训练配置
- [ ] 通过小测试验证实现
- [ ] 设置监控和日志记录

### 第3-6周：6层训练
- [ ] 训练6层模型至收敛
- [ ] 对照基线指标验证
- [ ] 优化超参数
- [ ] 记录性能改进

### 第7-10周：渐进式扩展
- [ ] 从6层检查点训练11层模型
- [ ] 从11层检查点训练完整13层模型
- [ ] 与官方GraphCast基准比较
- [ ] 微调以获得最大准确性

### 第11-12周：优化和验证
- [ ] 实现高级技术（课程学习等）
- [ ] 对测试数据集进行全面评估
- [ ] 性能分析和优化
- [ ] 文档和知识转移

本实现指南提供了一个实用的路径来集成所有已识别的改进，同时保持与现有PhysicsNemo代码库的兼容性。
